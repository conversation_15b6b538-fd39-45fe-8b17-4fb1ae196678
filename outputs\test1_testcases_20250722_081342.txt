================================================================================
TEST CASES - TEST1
Generated on: 2025-07-22 08:13:42
================================================================================


============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_001
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới chức danh
Độ ưu tiên: Mô tả và nút Lưu

Các bước thực hiện:
  1. Mở phân hệ Quản trị hệ thống
  2. Nhấn nút Tạo mới chức danh

Kết quả mong muốn:
  Popup Tạo mới chức danh hiển thị với các trường Tên chức danh

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_UI_002
Mục đích kiểm thử: Kiểm tra hiển thị các trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới chức danh
  2. Kiểm tra hiển thị dấu * cho trường bắt buộc

Kết quả mong muốn:
  Trường Tên chức danh hiển thị dấu * hoặc nhãn bắt buộc

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_UI_003
Mục đích kiểm thử: Kiểm tra popup Sửa chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở danh sách chức danh
  2. Chọn một chức danh từ danh sách
  3. Nhấn nút Sửa

Kết quả mong muốn:
  Popup Chỉnh sửa chức danh hiển thị với dữ liệu cũ được pre-fill

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_UI_004
Mục đích kiểm thử: Kiểm tra popup Xóa chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở danh sách chức danh
  2. Chọn một chức danh từ danh sách
  3. Nhấn nút Xóa

Kết quả mong muốn:
  Hiển thị popup xác nhận xóa chức danh

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_UI_005
Mục đích kiểm thử: Kiểm tra ô nhập Tìm kiếm chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở danh sách chức danh
  2. Kiểm tra giao diện có ô nhập Tìm kiếm

Kết quả mong muốn:
  Danh sách chức danh có ô nhập Tìm kiếm để filter

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TRƯỜNG
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_001
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: hiển thị thông báo thành công

Các bước thực hiện:
  1. Mở phân hệ Quản trị hệ thống
  2. Nhấn nút Tạo mới
  3. Nhập Tên chức danh (ví dụ: Quản lý) và Mô tả
  4. Nhấn Lưu

Kết quả mong muốn:
  Chức danh được thêm thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_002
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống Tên chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ Quản trị hệ thống
  2. Nhấn nút Tạo mới
  3. Để trống Tên chức danh
  4. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị lỗi Vui lòng nhập Tên chức danh

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_VAL_003
Mục đích kiểm thử: Kiểm tra sửa chức danh với thông tin hợp lệ
Độ ưu tiên: hiển thị thông báo thành công

Các bước thực hiện:
  1. Mở danh sách chức danh
  2. Chọn một chức danh từ danh sách
  3. Nhấn nút Sửa
  4. Chỉnh sửa thông tin
  5. Nhấn Lưu

Kết quả mong muốn:
  Chỉnh sửa chức danh thành công

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_VAL_004
Mục đích kiểm thử: Kiểm tra xóa chức danh đã được gán cho nhân viên
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở danh sách chức danh
  2. Chọn chức danh đang được gán cho nhân viên
  3. Nhấn Xóa

Kết quả mong muốn:
  Hiển thị thông báo không cho phép xóa

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_VAL_005
Mục đích kiểm thử: Kiểm tra tìm kiếm chức danh với từ khóa hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở danh sách chức danh
  2. Nhập từ khóa hợp lệ vào ô tìm kiếm
  3. Kiểm tra danh sách chức danh được filter

Kết quả mong muốn:
  Hiển thị danh sách chức danh được filter theo từ khóa

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_VAL_006
Mục đích kiểm thử: Kiểm tra tìm kiếm chức danh với từ khóa không phân biệt hoa thường
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở danh sách chức danh
  2. Nhập từ khóa không phân biệt hoa thường vào ô tìm kiếm
  3. Kiểm tra danh sách chức danh được filter không phân biệt hoa thường

Kết quả mong muốn:
  Hiển thị danh sách chức danh được filter không phân biệt hoa thường

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: TC_VAL_007
Mục đích kiểm thử: Kiểm tra trường Mô tả khi không nhập
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới chức danh
  2. Để trống trường Mô tả
  3. Nhấn Lưu

Kết quả mong muốn:
  Chức danh được thêm thành công mà không có mô tả

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: TC_VAL_008
Mục đích kiểm thử: Kiểm tra trường Mô tả vượt quá 256 ký tự
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup Tạo mới chức danh
  2. Nhập Mô tả với độ dài vượt quá 256 ký tự
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị lỗi Mô tả không được vượt quá 256 ký tự

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: TC_VAL_009
Mục đích kiểm thử: Kiểm tra sửa chức danh với trùng tên chức danh khác
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở danh sách chức danh
  2. Chọn một chức danh từ danh sách
  3. Nhấn nút Sửa
  4. Chỉnh sửa Tên chức danh trùng với chức danh khác
  5. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị lỗi Tên chức danh đã tồn tại

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: Đây là một số test case chi tiết và chuyên nghiệp để kiểm thử chức năng Quản lý chức danh. Bạn có thể mở rộng danh sách test case này bằng cách thêm các trường hợp biên
Mục đích kiểm thử: tình huống lỗi khác để đảm bảo tính toàn diện của bộ test case.
Độ ưu tiên: 

Các bước thực hiện:

Kết quả mong muốn:

--------------------------------------------------------------------------------


Total Test Cases Generated: 15
Generated by Test Case Generator v1.0
