# File Management System Documentation

## Overview

The Test Case Generator now includes a comprehensive file management system that provides detailed tracking, organization, and management of all uploaded requirements and generated test case files.

## 🚀 Key Features

### **File Metadata Tracking**
- **Unique File IDs**: Each file gets a UUID for precise tracking
- **File Size Monitoring**: Automatic file size calculation and human-readable formatting
- **Upload Timestamps**: Precise tracking of when files were uploaded or generated
- **Processing Status**: Track file processing state (pending/processed/error)
- **File Categories**: Automatic categorization as input (requirements) or output (test cases)

### **Standardized File Naming**
All generated output files follow a consistent naming convention:

**Format:** `{ProjectName}_TestCases_{Timestamp}_{ModelUsed}.{extension}`

**Examples:**
- `UserManagement_TestCases_20250122_143052_ChatGPT.txt`
- `LoginSystem_TestCases_20250122_143052_Gemini.xlsx`
- `PaymentModule_TestCases_20250122_143052_Mock.txt`

**Project Name Sanitization:**
- Special characters removed or replaced
- Spaces converted to underscores
- Multiple underscores collapsed to single
- Leading/trailing underscores removed

### **Project-Specific Organization**
- **Categorized Display**: Files grouped by input/output type
- **File Statistics**: Count and size summaries per project
- **Status Indicators**: Visual status icons for each file
- **Model Tracking**: Record which AI model generated each output

## 🎯 User Interface Features

### **File Management Dashboard**
Located in the Project Management section, the file management interface provides:

1. **Project Selection Dropdown**: Choose which project's files to view
2. **View Project Files Button**: Display detailed file information
3. **Comprehensive File Listing**: Shows all files with metadata

### **File Information Display**
For each file, the system shows:
- **File Type Icon**: Visual indicator (📄 .txt, 📝 .docx, 📊 .xlsx)
- **Status Icon**: ✅ Processed, ⏳ Pending
- **File Name**: Original filename
- **File Size**: Human-readable format (B, KB, MB, GB)
- **Date**: Upload or generation date
- **Processing Status**: Current processing state
- **AI Model**: Which model generated the file (for outputs)

### **Project Statistics**
- Total file count by category
- Combined file size
- Processing status summary

## 🔧 Technical Implementation

### **Enhanced Project Manager**
New methods added to `project_manager.py`:

```python
# File categorization
get_project_files_by_category(project_id) -> Dict[str, List]

# File statistics
get_file_stats(project_id) -> Dict[str, Any]

# Status management
update_file_processing_status(project_id, file_id, status)

# File deletion
delete_file_from_project(project_id, file_id) -> bool

# Utility functions
format_file_size(size_bytes) -> str
```

### **Standardized Output Handler**
Enhanced `output_handler.py` with:

```python
# Project name sanitization
sanitize_project_name(project_name) -> str

# Standardized filename generation
generate_filename(project_name, file_type, model_used) -> str
```

### **File Metadata Structure**
Each file entry now includes:

```json
{
  "id": "uuid-string",
  "path": "/path/to/file",
  "original_name": "filename.ext",
  "file_type": ".ext",
  "file_size": 1024,
  "uploaded_at": "2025-01-22T14:30:52",
  "processing_status": "processed",
  "category": "input|output",
  "model_used": "ChatGPT" // for output files
}
```

## 📋 Usage Guide

### **Viewing Project Files**
1. Select a project from the "File Management" dropdown
2. Click "📋 View Project Files"
3. Review the detailed file listing that appears

### **File Status Indicators**
- **⏳ Pending**: File uploaded but not yet processed
- **✅ Processed**: File successfully processed for test case generation
- **❌ Error**: Processing failed (if implemented)

### **Understanding File Categories**

#### **Input Files (📤)**
- Requirements documents uploaded by users
- Supported formats: .txt, .docx, .xlsx, .xls
- Status changes from "pending" to "processed" after test case generation

#### **Output Files (📋)**
- Generated test case files
- Always created in pairs: .txt and .xlsx
- Include AI model information in filename and metadata

## 🔄 File Lifecycle

1. **Upload**: User uploads requirement files → Status: "pending"
2. **Processing**: Test case generation uses files → Status: "processed"
3. **Output Creation**: System generates standardized output files
4. **Tracking**: All files tracked with comprehensive metadata

## 🛠️ Advanced Features

### **File Deletion**
- Files can be deleted from projects
- Physical files removed from storage
- Metadata cleaned from project records
- Confirmation required for safety

### **Backward Compatibility**
- Existing projects continue to work
- Legacy file entries automatically upgraded
- No data loss during system updates

### **Error Handling**
- Graceful handling of missing files
- Safe file operations with error recovery
- User-friendly error messages

## 📊 File Statistics

The system provides comprehensive statistics:
- **Total Files**: Count of all files in project
- **Input Files**: Count of uploaded requirement files
- **Output Files**: Count of generated test case files
- **Total Size**: Combined size of all files
- **Size Breakdown**: Human-readable format (KB, MB, GB)

## 🔮 Future Enhancements

### **Planned Features**
- **File Preview**: View file contents without downloading
- **Batch Operations**: Select and manage multiple files
- **File History**: Track file modifications and versions
- **Export Options**: Export file listings to CSV/Excel
- **Storage Analytics**: Detailed storage usage reports

### **Integration Possibilities**
- **Cloud Storage**: Integration with cloud storage providers
- **Version Control**: Git-like versioning for requirement files
- **Collaboration**: Multi-user file sharing and permissions
- **API Access**: REST API for external file management

## 🚨 Important Notes

### **File Storage**
- Files stored in local `data/uploads/` and `outputs/` directories
- Unique filenames prevent conflicts
- Automatic cleanup on project deletion

### **Performance Considerations**
- File metadata cached in project JSON
- Efficient file size calculations
- Optimized for projects with hundreds of files

### **Security**
- File paths validated to prevent directory traversal
- Safe filename sanitization
- Proper error handling for file operations

---

**File Management System v1.0** | Comprehensive file tracking and organization for professional test case generation workflows.
