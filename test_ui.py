#!/usr/bin/env python3
"""
Test script for the enhanced UI
"""

import os
import sys

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ui_creation():
    """Test that the UI can be created without errors"""
    print("🧪 Testing Enhanced UI Creation...")
    
    try:
        from app import TestCaseGeneratorApp
        
        # Create app instance
        app = TestCaseGeneratorApp()
        print("✅ App instance created successfully")
        
        # Create interface
        interface = app.create_interface()
        print("✅ Interface created successfully")
        
        # Check that interface is a Gradio Blocks object
        import gradio as gr
        assert isinstance(interface, gr.Blocks), "Interface should be a Gradio Blocks object"
        print("✅ Interface is valid Gradio Blocks object")
        
        print("\n🎉 UI Creation Test Passed!")
        print("The enhanced professional interface is ready to use.")
        
        return True
        
    except Exception as e:
        print(f"❌ UI Creation Test Failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_service_status():
    """Test AI service status display"""
    print("\n🤖 Testing AI Service Status...")
    
    try:
        from test_case_generator import TestCaseGenerator
        
        tcg = TestCaseGenerator()
        
        # Test available models
        models = tcg.get_available_ai_models()
        print(f"✅ Available AI models: {models}")
        
        # Test service status
        status = tcg.get_ai_service_status()
        print("✅ AI service status retrieved:")
        print(f"   - OpenAI: {'Available' if status['openai']['available'] else 'Not available'}")
        print(f"   - Gemini: {'Available' if status['gemini']['available'] else 'Not available'}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI Service Status Test Failed: {str(e)}")
        return False

def main():
    """Run UI tests"""
    print("🎨 Testing Enhanced Professional UI")
    print("=" * 50)
    
    success = True
    
    # Test UI creation
    if not test_ui_creation():
        success = False
    
    # Test AI service status
    if not test_ai_service_status():
        success = False
    
    print("\n" + "=" * 50)
    
    if success:
        print("🎉 ALL UI TESTS PASSED!")
        print("\n🚀 Enhanced Features:")
        print("   ✅ Professional English interface")
        print("   ✅ Modern color scheme and styling")
        print("   ✅ Improved button hierarchy")
        print("   ✅ Better visual organization")
        print("   ✅ Responsive layout design")
        print("   ✅ Professional tooltips and descriptions")
        
        print("\n📋 Key Improvements:")
        print("   • English button labels and messages")
        print("   • Professional card-based layout")
        print("   • Visual separators and grouping")
        print("   • Consistent branding and colors")
        print("   • Enhanced user experience")
        
        print("\n🎯 Ready for Enterprise Use!")
        print("Run: python app.py")
        
    else:
        print("❌ Some UI tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
