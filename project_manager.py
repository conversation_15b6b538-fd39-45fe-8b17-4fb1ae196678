"""
Project Management System for Test Case Generator
Handles project creation, storage, and file management
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
import uuid


class ProjectManager:
    """Manages projects and their associated files"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = data_dir
        self.projects_file = os.path.join(data_dir, "projects.json")
        self.ensure_data_directory()
        self.projects = self.load_projects()
    
    def ensure_data_directory(self):
        """Ensure data directory exists"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def load_projects(self) -> Dict[str, Any]:
        """Load projects from JSON file"""
        if os.path.exists(self.projects_file):
            try:
                with open(self.projects_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                return {}
        return {}
    
    def save_projects(self):
        """Save projects to JSON file"""
        with open(self.projects_file, 'w', encoding='utf-8') as f:
            json.dump(self.projects, f, ensure_ascii=False, indent=2)
    
    def create_project(self, name: str, description: str = "") -> str:
        """
        Create a new project
        
        Args:
            name: Project name
            description: Project description
            
        Returns:
            project_id: Unique project identifier
        """
        if not name.strip():
            raise ValueError("Project name cannot be empty")
        
        # Check if project name already exists
        for project_id, project in self.projects.items():
            if project['name'].lower() == name.lower():
                raise ValueError(f"Project '{name}' already exists")
        
        project_id = str(uuid.uuid4())
        project_data = {
            'id': project_id,
            'name': name.strip(),
            'description': description.strip(),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'files': [],
            'generated_outputs': []
        }
        
        self.projects[project_id] = project_data
        self.save_projects()
        
        return project_id
    
    def get_project(self, project_id: str) -> Optional[Dict[str, Any]]:
        """Get project by ID"""
        return self.projects.get(project_id)
    
    def get_project_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """Get project by name"""
        for project in self.projects.values():
            if project['name'].lower() == name.lower():
                return project
        return None
    
    def list_projects(self) -> List[Dict[str, Any]]:
        """List all projects"""
        return list(self.projects.values())
    
    def get_project_names(self) -> List[str]:
        """Get list of project names"""
        return [project['name'] for project in self.projects.values()]
    
    def update_project(self, project_id: str, name: str = None, description: str = None):
        """Update project details"""
        if project_id not in self.projects:
            raise ValueError(f"Project with ID '{project_id}' not found")
        
        project = self.projects[project_id]
        
        if name is not None:
            # Check if new name conflicts with existing projects
            for pid, p in self.projects.items():
                if pid != project_id and p['name'].lower() == name.lower():
                    raise ValueError(f"Project '{name}' already exists")
            project['name'] = name.strip()
        
        if description is not None:
            project['description'] = description.strip()
        
        project['updated_at'] = datetime.now().isoformat()
        self.save_projects()
    
    def delete_project(self, project_id: str):
        """Delete a project and its associated files"""
        if project_id not in self.projects:
            raise ValueError(f"Project with ID '{project_id}' not found")
        
        # Clean up associated files
        project = self.projects[project_id]
        for file_info in project.get('files', []):
            file_path = file_info.get('path')
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except OSError:
                    pass  # File might be in use or already deleted
        
        # Clean up generated outputs
        for output_info in project.get('generated_outputs', []):
            for file_path in [output_info.get('txt_path'), output_info.get('excel_path')]:
                if file_path and os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                    except OSError:
                        pass
        
        del self.projects[project_id]
        self.save_projects()
    
    def add_file_to_project(self, project_id: str, file_path: str, original_name: str, file_type: str):
        """Add a file to a project"""
        if project_id not in self.projects:
            raise ValueError(f"Project with ID '{project_id}' not found")

        # Get file size
        file_size = 0
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)

        file_info = {
            'id': str(uuid.uuid4()),
            'path': file_path,
            'original_name': original_name,
            'file_type': file_type,
            'file_size': file_size,
            'uploaded_at': datetime.now().isoformat(),
            'processing_status': 'pending',
            'category': 'input'  # input or output
        }

        self.projects[project_id]['files'].append(file_info)
        self.projects[project_id]['updated_at'] = datetime.now().isoformat()
        self.save_projects()
    
    def add_generated_output(self, project_id: str, txt_path: str, excel_path: str, model_used: str = "Mock"):
        """Add generated output files to a project"""
        if project_id not in self.projects:
            raise ValueError(f"Project with ID '{project_id}' not found")

        # Get file sizes
        txt_size = os.path.getsize(txt_path) if os.path.exists(txt_path) else 0
        excel_size = os.path.getsize(excel_path) if os.path.exists(excel_path) else 0

        # Add TXT file info
        txt_file_info = {
            'id': str(uuid.uuid4()),
            'path': txt_path,
            'original_name': os.path.basename(txt_path),
            'file_type': '.txt',
            'file_size': txt_size,
            'uploaded_at': datetime.now().isoformat(),
            'processing_status': 'processed',
            'category': 'output',
            'model_used': model_used
        }

        # Add Excel file info
        excel_file_info = {
            'id': str(uuid.uuid4()),
            'path': excel_path,
            'original_name': os.path.basename(excel_path),
            'file_type': '.xlsx',
            'file_size': excel_size,
            'uploaded_at': datetime.now().isoformat(),
            'processing_status': 'processed',
            'category': 'output',
            'model_used': model_used
        }

        # Add to files list
        self.projects[project_id]['files'].extend([txt_file_info, excel_file_info])

        # Keep legacy generated_outputs for backward compatibility
        output_info = {
            'txt_path': txt_path,
            'excel_path': excel_path,
            'generated_at': datetime.now().isoformat(),
            'model_used': model_used
        }

        self.projects[project_id]['generated_outputs'].append(output_info)
        self.projects[project_id]['updated_at'] = datetime.now().isoformat()
        self.save_projects()
    
    def get_project_files(self, project_id: str) -> List[Dict[str, Any]]:
        """Get all files associated with a project"""
        project = self.get_project(project_id)
        if not project:
            return []
        return project.get('files', [])

    def get_project_files_by_category(self, project_id: str) -> Dict[str, List[Dict[str, Any]]]:
        """Get project files organized by category (input/output)"""
        files = self.get_project_files(project_id)
        categorized = {'input': [], 'output': []}

        for file_info in files:
            category = file_info.get('category', 'input')
            categorized[category].append(file_info)

        return categorized

    def update_file_processing_status(self, project_id: str, file_id: str, status: str):
        """Update the processing status of a file"""
        if project_id not in self.projects:
            raise ValueError(f"Project with ID '{project_id}' not found")

        for file_info in self.projects[project_id]['files']:
            if file_info.get('id') == file_id:
                file_info['processing_status'] = status
                file_info['updated_at'] = datetime.now().isoformat()
                break

        self.projects[project_id]['updated_at'] = datetime.now().isoformat()
        self.save_projects()

    def delete_file_from_project(self, project_id: str, file_id: str) -> bool:
        """Delete a file from a project"""
        if project_id not in self.projects:
            raise ValueError(f"Project with ID '{project_id}' not found")

        files = self.projects[project_id]['files']
        for i, file_info in enumerate(files):
            if file_info.get('id') == file_id:
                # Delete physical file
                file_path = file_info.get('path')
                if file_path and os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                    except OSError:
                        pass  # File might be in use

                # Remove from project
                files.pop(i)
                self.projects[project_id]['updated_at'] = datetime.now().isoformat()
                self.save_projects()
                return True

        return False

    def get_file_stats(self, project_id: str) -> Dict[str, Any]:
        """Get file statistics for a project"""
        files = self.get_project_files(project_id)
        categorized = self.get_project_files_by_category(project_id)

        total_size = sum(file_info.get('file_size', 0) for file_info in files)

        return {
            'total_files': len(files),
            'input_files': len(categorized['input']),
            'output_files': len(categorized['output']),
            'total_size': total_size,
            'total_size_mb': round(total_size / (1024 * 1024), 2) if total_size > 0 else 0
        }

    def format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"
