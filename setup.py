#!/usr/bin/env python3
"""
Setup script for Test Case Generator
Helps users set up the application with proper configuration
"""

import os
import shutil
import sys
from pathlib import Path


def create_env_file():
    """Create .env file from template if it doesn't exist"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        shutil.copy2(env_example, env_file)
        print("✅ Created .env file from template")
        print("📝 Please edit .env file and add your API keys")
        return True
    elif env_file.exists():
        print("ℹ️ .env file already exists")
        return False
    else:
        print("⚠️ .env.example not found")
        return False


def create_directories():
    """Create necessary directories"""
    directories = ["data", "data/uploads", "outputs", "templates"]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")


def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        "gradio",
        "pandas", 
        "openpyxl",
        "python-docx",
        "python-dotenv"
    ]
    
    optional_packages = [
        "openai",
        "google-generativeai"
    ]
    
    missing_required = []
    missing_optional = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package} is installed")
        except ImportError:
            missing_required.append(package)
            print(f"❌ {package} is missing")
    
    for package in optional_packages:
        try:
            if package == "google-generativeai":
                __import__("google.generativeai")
            else:
                __import__(package.replace("-", "_"))
            print(f"✅ {package} is installed")
        except ImportError:
            missing_optional.append(package)
            print(f"⚠️ {package} is missing (optional for AI features)")
    
    return missing_required, missing_optional


def print_setup_instructions():
    """Print setup instructions"""
    print("\n" + "="*60)
    print("🚀 TEST CASE GENERATOR SETUP")
    print("="*60)
    
    print("\n📦 STEP 1: Install Dependencies")
    print("Run the following command to install required packages:")
    print("   pip install -r requirements.txt")
    
    print("\n🔑 STEP 2: Configure API Keys (Optional)")
    print("To use AI-powered test case generation:")
    print("1. Edit the .env file")
    print("2. Add your API keys:")
    print("   OPENAI_API_KEY=your_openai_key_here")
    print("   GEMINI_API_KEY=your_gemini_key_here")
    print("\n📍 Get API keys from:")
    print("   • OpenAI: https://platform.openai.com/api-keys")
    print("   • Gemini: https://makersuite.google.com/app/apikey")
    
    print("\n🏃 STEP 3: Run the Application")
    print("   python app.py")
    print("   Then open: http://localhost:7860")
    
    print("\n🧪 STEP 4: Test the Setup (Optional)")
    print("   python test_enhanced_app.py")
    
    print("\n💡 FEATURES:")
    print("   ✅ Project management")
    print("   ✅ File upload (.txt, .docx, .xlsx, .xls)")
    print("   ✅ AI-powered test case generation")
    print("   ✅ Professional Vietnamese test cases")
    print("   ✅ Export to TXT and Excel formats")
    print("   ✅ Fallback mock generation")


def main():
    """Main setup function"""
    print("🔧 Setting up Test Case Generator...")
    print("-" * 40)
    
    # Create directories
    print("\n📁 Creating directories...")
    create_directories()
    
    # Create .env file
    print("\n⚙️ Setting up configuration...")
    env_created = create_env_file()
    
    # Check dependencies
    print("\n📦 Checking dependencies...")
    missing_required, missing_optional = check_dependencies()
    
    # Print results
    print("\n" + "="*40)
    print("SETUP SUMMARY")
    print("="*40)
    
    if missing_required:
        print(f"❌ Missing required packages: {', '.join(missing_required)}")
        print("   Run: pip install -r requirements.txt")
    else:
        print("✅ All required packages are installed")
    
    if missing_optional:
        print(f"⚠️ Missing optional packages: {', '.join(missing_optional)}")
        print("   These are needed for AI features")
    else:
        print("✅ All optional packages are installed")
    
    if env_created:
        print("📝 Please configure your API keys in .env file")
    
    print("\n✅ Setup completed!")
    
    # Print instructions
    print_setup_instructions()


if __name__ == "__main__":
    main()
