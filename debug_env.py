#!/usr/bin/env python3
"""
Debug script to check environment variable loading
"""

import os
from dotenv import load_dotenv

print("🔍 Debugging Environment Variables")
print("=" * 50)

# Check if .env file exists
env_file = ".env"
if os.path.exists(env_file):
    print(f"✅ .env file exists at: {os.path.abspath(env_file)}")
    
    # Read and display .env file content (masking sensitive parts)
    with open(env_file, 'r') as f:
        lines = f.readlines()
    
    print(f"📄 .env file content ({len(lines)} lines):")
    for i, line in enumerate(lines, 1):
        line = line.strip()
        if line and not line.startswith('#'):
            if '=' in line:
                key, value = line.split('=', 1)
                if 'KEY' in key.upper():
                    # Mask API keys for security
                    masked_value = value[:10] + "..." + value[-10:] if len(value) > 20 else "***"
                    print(f"  {i}: {key}={masked_value}")
                else:
                    print(f"  {i}: {key}={value}")
            else:
                print(f"  {i}: {line}")
        elif line:
            print(f"  {i}: {line}")
else:
    print(f"❌ .env file not found at: {os.path.abspath(env_file)}")

print("\n🔄 Loading environment variables...")

# Load environment variables
result = load_dotenv()
print(f"load_dotenv() result: {result}")

print("\n🔑 Checking environment variables:")

# Check specific variables
env_vars = [
    'OPENAI_API_KEY',
    'GEMINI_API_KEY', 
    'OPENAI_MODEL',
    'GEMINI_MODEL',
    'API_MAX_TOKENS',
    'API_TIMEOUT'
]

for var in env_vars:
    value = os.getenv(var)
    if value:
        if 'KEY' in var:
            # Mask API keys
            masked = value[:10] + "..." + value[-10:] if len(value) > 20 else "***"
            print(f"  ✅ {var}: {masked}")
        else:
            print(f"  ✅ {var}: {value}")
    else:
        print(f"  ❌ {var}: Not found")

print("\n🧪 Testing AI Service initialization...")

try:
    from ai_service import AIService
    
    ai_service = AIService()
    
    print(f"OpenAI API Key loaded: {'Yes' if ai_service.openai_api_key else 'No'}")
    print(f"Gemini API Key loaded: {'Yes' if ai_service.gemini_api_key else 'No'}")
    
    status = ai_service.get_service_status()
    print(f"\nService Status:")
    print(f"  OpenAI available: {status['openai']['available']}")
    print(f"  OpenAI configured: {status['openai']['configured']}")
    print(f"  Gemini available: {status['gemini']['available']}")
    print(f"  Gemini configured: {status['gemini']['configured']}")
    
    available_models = ai_service.get_available_models()
    print(f"  Available models: {available_models}")
    
except Exception as e:
    print(f"❌ Error initializing AI Service: {e}")
    import traceback
    traceback.print_exc()

print("\n📦 Checking required packages...")

packages = [
    ('python-dotenv', 'dotenv'),
    ('openai', 'openai'),
    ('google-generativeai', 'google.generativeai')
]

for package_name, import_name in packages:
    try:
        if import_name == 'google.generativeai':
            import google.generativeai
        else:
            __import__(import_name)
        print(f"  ✅ {package_name}: Installed")
    except ImportError:
        print(f"  ❌ {package_name}: Not installed")

print("\n" + "=" * 50)
print("🔍 Debug completed!")
