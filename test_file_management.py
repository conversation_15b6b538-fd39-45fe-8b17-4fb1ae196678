#!/usr/bin/env python3
"""
Test script for the file management features
"""

import os
import sys
import tempfile
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from project_manager import ProjectManager
from output_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>


def test_project_manager_enhancements():
    """Test enhanced project manager functionality"""
    print("🧪 Testing Enhanced Project Manager...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        pm = ProjectManager(temp_dir)
        
        # Create test project
        project_id = pm.create_project("Test File Management", "Testing file management features")
        print(f"✅ Created project: {project_id}")
        
        # Create test files
        test_files = []
        for i in range(3):
            test_file = os.path.join(temp_dir, f"test_file_{i}.txt")
            with open(test_file, 'w') as f:
                f.write(f"Test content for file {i}")
            test_files.append(test_file)
        
        # Add files to project
        for i, file_path in enumerate(test_files):
            pm.add_file_to_project(project_id, file_path, f"test_file_{i}.txt", ".txt")
            print(f"✅ Added file {i} to project")
        
        # Test file categorization
        categorized = pm.get_project_files_by_category(project_id)
        assert len(categorized['input']) == 3
        assert len(categorized['output']) == 0
        print("✅ File categorization works")
        
        # Test file stats
        stats = pm.get_file_stats(project_id)
        assert stats['total_files'] == 3
        assert stats['input_files'] == 3
        assert stats['output_files'] == 0
        print(f"✅ File stats: {stats}")
        
        # Test file size formatting
        size_str = pm.format_file_size(1024)
        assert "KB" in size_str
        print(f"✅ File size formatting: {size_str}")
        
        # Test file status update
        files = pm.get_project_files(project_id)
        if files:
            file_id = files[0]['id']
            pm.update_file_processing_status(project_id, file_id, 'processed')
            updated_files = pm.get_project_files(project_id)
            assert updated_files[0]['processing_status'] == 'processed'
            print("✅ File status update works")
        
        print("✅ Enhanced Project Manager tests passed!\n")


def test_output_handler_naming():
    """Test standardized output file naming"""
    print("🧪 Testing Standardized File Naming...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        oh = OutputHandler(temp_dir)
        
        # Test project name sanitization
        test_cases = [
            ("User Management System", "User_Management_System"),
            ("Login & Authentication", "Login_Authentication"),
            ("Test-Project_Name", "Test_Project_Name"),
            ("   Spaces   ", "Spaces"),
            ("Special@#$%Characters", "SpecialCharacters"),
            ("", "project")
        ]
        
        for input_name, expected in test_cases:
            result = oh.sanitize_project_name(input_name)
            assert result == expected, f"Expected {expected}, got {result}"
            print(f"✅ Sanitization: '{input_name}' -> '{result}'")
        
        # Test filename generation
        filename = oh.generate_filename("Test Project", "txt", "ChatGPT")
        assert "Test_Project_TestCases_" in filename
        assert "_ChatGPT.txt" in filename
        print(f"✅ Generated filename: {filename}")
        
        # Test with different models
        models = ["ChatGPT", "Gemini", "Mock (Built-in Generator)"]
        for model in models:
            filename = oh.generate_filename("Sample Project", "xlsx", model)
            print(f"✅ {model} filename: {filename}")
        
        print("✅ Standardized File Naming tests passed!\n")


def test_file_management_integration():
    """Test full file management integration"""
    print("🧪 Testing File Management Integration...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        pm = ProjectManager(os.path.join(temp_dir, "data"))
        oh = OutputHandler(os.path.join(temp_dir, "outputs"))
        
        # Create project
        project_id = pm.create_project("Integration Test", "Testing integration")
        
        # Add input files
        input_files = []
        for i in range(2):
            test_file = os.path.join(temp_dir, f"requirement_{i}.txt")
            with open(test_file, 'w') as f:
                f.write(f"Requirement {i}: Test functionality")
            input_files.append(test_file)
            pm.add_file_to_project(project_id, test_file, f"requirement_{i}.txt", ".txt")
        
        # Generate output files
        sample_test_cases = [
            {
                'ID': 'TC_001',
                'Mục đích kiểm thử': 'Test case 1',
                'Các bước thực hiện': 'Step 1\nStep 2',
                'Kết quả mong muốn': 'Expected result',
                'Độ ưu tiên': 'High'
            }
        ]
        
        txt_path, excel_path = oh.generate_output_files(
            sample_test_cases, 
            "Integration Test", 
            "ChatGPT"
        )
        
        # Add output files to project
        pm.add_generated_output(project_id, txt_path, excel_path, "ChatGPT")
        
        # Verify file organization
        categorized = pm.get_project_files_by_category(project_id)
        assert len(categorized['input']) == 2
        assert len(categorized['output']) == 2  # TXT and Excel
        
        stats = pm.get_file_stats(project_id)
        assert stats['total_files'] == 4
        assert stats['input_files'] == 2
        assert stats['output_files'] == 2
        
        print(f"✅ Integration test completed:")
        print(f"   - Input files: {stats['input_files']}")
        print(f"   - Output files: {stats['output_files']}")
        print(f"   - Total size: {pm.format_file_size(stats['total_size'])}")
        
        # Test file deletion
        files = pm.get_project_files(project_id)
        if files:
            file_id = files[0]['id']
            success = pm.delete_file_from_project(project_id, file_id)
            assert success
            print("✅ File deletion works")
        
        print("✅ File Management Integration tests passed!\n")


def main():
    """Run all file management tests"""
    print("📁 Testing File Management Features")
    print("=" * 50)
    
    try:
        test_project_manager_enhancements()
        test_output_handler_naming()
        test_file_management_integration()
        
        print("=" * 50)
        print("🎉 ALL FILE MANAGEMENT TESTS PASSED! 🎉")
        print("\n🚀 **New File Management Features:**")
        print("  ✅ Comprehensive file metadata tracking")
        print("  ✅ Standardized output file naming")
        print("  ✅ File categorization (input/output)")
        print("  ✅ File size and status tracking")
        print("  ✅ Project-specific file organization")
        print("  ✅ File deletion with cleanup")
        print("  ✅ File statistics and reporting")
        
        print("\n📋 **File Naming Convention:**")
        print("  Format: {ProjectName}_TestCases_{Timestamp}_{Model}.{ext}")
        print("  Example: UserManagement_TestCases_20250122_143052_ChatGPT.txt")
        
        print("\n🎯 **Ready for Production Use!**")
        print("The file management system is fully integrated and tested.")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
