# Changelog - Test Case Generator

## Version 2.0.0 - Enhanced AI Integration

### 🚀 New Features

#### AI-Powered Test Case Generation
- **ChatGPT Integration**: Added OpenAI API support for intelligent test case generation
- **Gemini Integration**: Added Google Gemini API support as an alternative AI model
- **Model Selection**: Dropdown interface to choose between ChatGPT, Gemini, or Mock generation
- **Professional Vietnamese Prompts**: Expertly crafted prompts for comprehensive test case generation
- **Fallback System**: Automatically falls back to mock generation if AI services fail

#### Enhanced User Interface
- **AI Model Dropdown**: Select your preferred AI model for test case generation
- **Service Status Display**: Real-time status of AI services and configuration
- **Improved Layout**: Better organization of UI components
- **Configuration Guidance**: Built-in instructions for API key setup

#### Environment Configuration
- **Environment Variables**: Support for `.env` file configuration
- **API Key Management**: Secure storage of OpenAI and Gemini API keys
- **Model Configuration**: Customizable model settings (GPT-3.5-turbo, Gemini-pro, etc.)
- **Timeout Settings**: Configurable API timeout values

### 🐛 Bug Fixes

#### File Upload Issues
- **Fixed Path Handling**: Resolved file upload errors with complex Gradio temporary file paths
- **Filename Sanitization**: Proper handling of filenames with path separators
- **Cross-Platform Compatibility**: Better support for Windows and Linux file paths

#### Interface Improvements
- **Gradio Compatibility**: Fixed `scale` parameter issue with Markdown components
- **Download Buttons**: Improved file download functionality
- **Error Handling**: Better error messages and user feedback

### 📁 New Files

#### Core AI Integration
- `ai_service.py` - AI service integration for OpenAI and Gemini APIs
- `.env.example` - Template for environment configuration
- `setup.py` - Automated setup script for easy installation

#### Enhanced Testing
- `test_enhanced_app.py` - Comprehensive test suite for AI features
- `CHANGELOG.md` - This changelog file

### 📦 Dependencies Added

```
google-generativeai>=0.3.0  # For Gemini API integration
python-dotenv>=1.0.0        # For environment variable management
```

### 🔧 Configuration

#### Required Setup
1. Copy `.env.example` to `.env`
2. Add your API keys:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   GEMINI_API_KEY=your_gemini_api_key_here
   ```

#### Optional Configuration
```
OPENAI_MODEL=gpt-3.5-turbo
GEMINI_MODEL=gemini-pro
OPENAI_MAX_TOKENS=4000
GEMINI_MAX_TOKENS=4000
API_TIMEOUT=30
```

### 🎯 Usage Examples

#### With AI Models
1. Select "ChatGPT" or "Gemini" from the AI model dropdown
2. Upload your requirement files (.txt, .docx, .xlsx, .xls)
3. Click "Generate Test Cases"
4. Get professional Vietnamese test cases powered by AI

#### Without AI (Mock Generation)
1. Select "Mock (Không dùng AI)" from the dropdown
2. Upload your requirement files
3. Click "Generate Test Cases"
4. Get structured test cases using built-in generation logic

### 📊 Test Case Quality

#### AI-Generated Features
- **Comprehensive Coverage**: UI, validation, functional, security, and performance testing
- **Professional Structure**: ID, Purpose, Steps, Expected Results, Priority
- **Vietnamese Terminology**: Professional Vietnamese testing terminology
- **Edge Cases**: Boundary testing and error scenarios
- **Categorization**: Organized test cases with clear categories

#### Example Output Structure
```
TC_UI_001: Kiểm tra hiển thị popup Tạo mới
TC_VAL_001: Kiểm tra validation với dữ liệu hợp lệ
TC_VAL_002: Kiểm tra validation với dữ liệu không hợp lệ
TC_FUN_001: Kiểm tra chức năng tạo mới thành công
TC_SEC_001: Kiểm tra bảo mật và phân quyền
```

### 🔄 Migration from v1.0

#### For Existing Users
1. Update dependencies: `pip install -r requirements.txt`
2. Run setup script: `python setup.py`
3. Configure API keys in `.env` file (optional)
4. Existing projects and files remain compatible

#### Breaking Changes
- None - Full backward compatibility maintained

### 🚀 Performance Improvements

#### AI Integration
- **Async Processing**: Better handling of API calls
- **Error Recovery**: Graceful fallback to mock generation
- **Response Parsing**: Improved CSV parsing from AI responses

#### File Handling
- **Path Resolution**: Better handling of complex file paths
- **Memory Usage**: Optimized file processing for large documents
- **Cross-Platform**: Improved compatibility across operating systems

### 🔮 Future Enhancements

#### Planned Features
- **Custom Prompts**: User-defined prompt templates
- **Batch Processing**: Multiple file processing
- **Template Library**: Pre-built test case templates
- **API Integration**: REST API for external tool integration
- **Collaboration**: Multi-user project sharing

#### AI Model Expansion
- **Claude Integration**: Anthropic Claude API support
- **Local Models**: Support for local LLM models
- **Custom Fine-tuning**: Domain-specific model training

### 📞 Support

#### Getting Help
- Check the README.md for detailed setup instructions
- Run `python test_enhanced_app.py` to verify your setup
- Review the `.env.example` file for configuration options

#### API Key Resources
- **OpenAI**: https://platform.openai.com/api-keys
- **Google Gemini**: https://makersuite.google.com/app/apikey

---

**Version 2.0.0** represents a major enhancement to the Test Case Generator with professional AI integration, improved user experience, and robust error handling while maintaining full backward compatibility.
