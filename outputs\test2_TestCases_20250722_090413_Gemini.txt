================================================================================
TEST CASES - TEST2
Generated on: 2025-07-22 09:04:13
================================================================================


============================================================
**KIỂM TRA MÀN HÌNH QUẢN LÝ MẪU ĐÁNH GIÁ**
============================================================

Test Case #1
----------------------------------------
ID: TC_QLMDG_UI_001
Mục đích kiểm thử: Kiểm tra hiển thị màn hình Quản lý Mẫu đánh giá
Độ ưu tiên: Button Thêm mới

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá

Kết quả mong muốn:
  Màn hình Quản lý Mẫu đánh giá hiển thị với các thành phần: Bộ lọc

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_QLMDG_UI_002
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của các thành phần
Độ ưu tiên: trừ button Lưu/Lưu cập nhật disable

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá

Kết quả mong muốn:
  Mọi input/button đều enable

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_QLMDG_FILTER_001
Mục đích kiểm thử: Kiểm tra hiển thị ô input bộ lọc
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá

Kết quả mong muốn:
  Hiển thị ô input nhập điều kiện tìm kiếm (có thể theo Phòng ban hoặc Vị trí)

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_QLMDG_FILTER_002
Mục đích kiểm thử: Kiểm tra chức năng bộ lọc theo phòng ban
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhập tên phòng ban vào ô bộ lọc
  3. Nhấn Enter

Kết quả mong muốn:
  Danh sách mẫu đánh giá hiển thị các mẫu thuộc phòng ban được chọn

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_QLMDG_FILTER_003
Mục đích kiểm thử: Kiểm tra chức năng bộ lọc theo vị trí
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhập tên vị trí vào ô bộ lọc
  3. Nhấn Enter

Kết quả mong muốn:
  Danh sách mẫu đánh giá hiển thị các mẫu áp dụng cho vị trí được chọn

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_QLMDG_FILTER_004
Mục đích kiểm thử: Kiểm tra chức năng bộ lọc khi không tìm thấy kết quả
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhập một giá trị không tồn tại vào ô bộ lọc
  3. Nhấn Enter

Kết quả mong muốn:
  Hiển thị thông báo Không tìm thấy kết quả

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: TC_QLMDG_DISPLAY_001
Mục đích kiểm thử: Kiểm tra hiển thị tên mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá

Kết quả mong muốn:
  Tên mẫu đánh giá hiển thị đúng như đã được đăng ký

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: TC_QLMDG_DISPLAY_002
Mục đích kiểm thử: Kiểm tra hiển thị phòng ban áp dụng
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá

Kết quả mong muốn:
  Phòng ban áp dụng hiển thị đúng như đã được đăng ký

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: TC_QLMDG_DISPLAY_003
Mục đích kiểm thử: Kiểm tra hiển thị vị trí áp dụng
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá

Kết quả mong muốn:
  Vị trí áp dụng hiển thị đúng như đã được đăng ký

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: TC_QLMDG_BUTTON_001
Mục đích kiểm thử: Kiểm tra hiển thị button Chỉnh sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá

Kết quả mong muốn:
  Button Chỉnh sửa hiển thị tương ứng với mỗi mẫu đánh giá

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: TC_QLMDG_BUTTON_002
Mục đích kiểm thử: Kiểm tra hiển thị button Xóa
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá

Kết quả mong muốn:
  Button Xóa hiển thị tương ứng với mỗi mẫu đánh giá

--------------------------------------------------------------------------------



============================================================
**KIỂM TRA POPUP TẠO MỚI MẪU ĐÁNH GIÁ**
============================================================

Test Case #1
----------------------------------------
ID: TC_QLMDG_CREATE_UI_001
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới mẫu đánh giá
Độ ưu tiên: Phòng áp dụng

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhấn button Thêm mới

Kết quả mong muốn:
  Popup Tạo mới mẫu đánh giá hiển thị với các trường: Tên mẫu

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_QLMDG_CREATE_FIELD_001
Mục đích kiểm thử: Kiểm tra hiển thị mặc định loại câu hỏi
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhấn button Thêm mới

Kết quả mong muốn:
  Dropdown chọn loại câu hỏi hiển thị mặc định là Trả lời ngắn

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_QLMDG_CREATE_FIELD_002
Mục đích kiểm thử: Kiểm tra hiển thị trạng thái mặc định của Switch
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhấn button Thêm mới

Kết quả mong muốn:
  Switch hiển thị ở trạng thái tắt (không bắt buộc)

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_QLMDG_CREATE_BUTTON_001
Mục đích kiểm thử: Kiểm tra chức năng button Hủy trong popup Tạo mới
Độ ưu tiên: không có mẫu đánh giá mới nào được tạo

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhấn button Thêm mới
  3. Nhấn button Hủy

Kết quả mong muốn:
  Popup Tạo mới mẫu đánh giá đóng lại

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_QLMDG_CREATE_BUTTON_002
Mục đích kiểm thử: Kiểm tra trạng thái button Lưu trong popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhấn button Thêm mới

Kết quả mong muốn:
  Button Lưu disable khi chưa điền thông tin vào form

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_QLMDG_CREATE_VALIDATE_001
Mục đích kiểm thử: Kiểm tra tạo mới mẫu đánh giá thành công
Độ ưu tiên: Vị trí áp dụng

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhấn button Thêm mới
  3. Nhập đầy đủ thông tin hợp lệ vào các trường: Tên mẫu

Kết quả mong muốn:
  Phòng áp dụng

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: TC_QLMDG_CREATE_VALIDATE_002
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống Tên mẫu
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhấn button Thêm mới
  3. Bỏ trống trường Tên mẫu
  4. Nhấn button Lưu

Kết quả mong muốn:
  Hiển thị lỗi Vui lòng nhập Tên mẫu

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: TC_QLMDG_CREATE_VALIDATE_003
Mục đích kiểm thử: Kiểm tra tạo mới với Tên mẫu trùng lặp
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhấn button Thêm mới
  3. Nhập Tên mẫu đã tồn tại
  4. Nhấn button Lưu

Kết quả mong muốn:
  Hiển thị lỗi Tên mẫu đã tồn tại

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: TC_QLMDG_CREATE_VALIDATE_004
Mục đích kiểm thử: Kiểm tra ràng buộc độ dài tối đa của Tên mẫu
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhấn button Thêm mới
  3. Nhập Tên mẫu vượt quá 255 ký tự
  4. Nhấn button Lưu

Kết quả mong muốn:
  Hiển thị lỗi Tên mẫu không được vượt quá 255 ký tự

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: TC_QLMDG_CREATE_VALIDATE_005
Mục đích kiểm thử: Kiểm tra tạo mới khi chưa chọn Phòng áp dụng
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhấn button Thêm mới
  3. Bỏ trống trường Phòng áp dụng
  4. Nhấn button Lưu

Kết quả mong muốn:
  Hiển thị lỗi Vui lòng chọn Phòng áp dụng

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: TC_QLMDG_CREATE_VALIDATE_006
Mục đích kiểm thử: Kiểm tra tạo mới khi chưa chọn Vị trí áp dụng
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhấn button Thêm mới
  3. Bỏ trống trường Vị trí áp dụng
  4. Nhấn button Lưu

Kết quả mong muốn:
  Hiển thị lỗi Vui lòng chọn Vị trí áp dụng

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: TC_QLMDG_CREATE_VALIDATE_007
Mục đích kiểm thử: Kiểm tra chức năng button Thêm câu hỏi
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhấn button Thêm mới
  3. Nhấn button Thêm câu hỏi

Kết quả mong muốn:
  Một câu hỏi mới được thêm vào phía dưới

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: TC_QLMDG_CREATE_VALIDATE_008
Mục đích kiểm thử: Kiểm tra chức năng button Xóa câu hỏi
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhấn button Thêm mới
  3. Nhấn button Thêm câu hỏi
  4. Nhấn button Xóa ở câu hỏi vừa thêm

Kết quả mong muốn:
  Câu hỏi vừa thêm bị xóa khỏi biểu mẫu

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: TC_QLMDG_CREATE_VALIDATE_009
Mục đích kiểm thử: Kiểm tra chức năng Switch bắt buộc trả lời
Độ ưu tiên: câu hỏi có Switch đang bật sẽ là bắt buộc phải trả lời

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhấn button Thêm mới
  3. Bật Switch ở một câu hỏi
  4. Nhấn button Lưu

Kết quả mong muốn:
  Khi người dùng thực hiện đánh giá trên biểu mẫu này

--------------------------------------------------------------------------------

Test Case #15
----------------------------------------
ID: TC_QLMDG_CREATE_VALIDATE_010
Mục đích kiểm thử: Kiểm tra ràng buộc độ dài tối đa của Tiêu đề câu hỏi
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhấn button Thêm mới
  3. Nhập Tiêu đề câu hỏi vượt quá 255 ký tự
  4. Nhấn button Lưu

Kết quả mong muốn:
  Hiển thị lỗi Tiêu đề câu hỏi không được vượt quá 255 ký tự

--------------------------------------------------------------------------------

Test Case #16
----------------------------------------
ID: TC_QLMDG_CREATE_VALIDATE_011
Mục đích kiểm thử: Kiểm tra các loại câu hỏi trong dropdown
Độ ưu tiên: Trả lời dài

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhấn button Thêm mới
  3. Click vào dropdown chọn loại câu hỏi

Kết quả mong muốn:
  Dropdown hiển thị các loại câu hỏi có sẵn (ví dụ: Trả lời ngắn

--------------------------------------------------------------------------------



============================================================
**KIỂM TRA POPUP CHỈNH SỬA MẪU ĐÁNH GIÁ**
============================================================

Test Case #1
----------------------------------------
ID: TC_QLMDG_EDIT_UI_001
Mục đích kiểm thử: Kiểm tra hiển thị popup Chỉnh sửa mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Chọn một mẫu đánh giá trong danh sách
  3. Nhấn button Chỉnh sửa

Kết quả mong muốn:
  Popup Chỉnh sửa mẫu đánh giá hiển thị với các trường đã được điền thông tin tương ứng với mẫu đánh giá được chọn

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_QLMDG_EDIT_FIELD_001
Mục đích kiểm thử: Kiểm tra hiển thị thông tin đã có trong popup Chỉnh sửa
Độ ưu tiên: Phòng áp dụng

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Chọn một mẫu đánh giá trong danh sách
  3. Nhấn button Chỉnh sửa

Kết quả mong muốn:
  Các trường Tên mẫu

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_QLMDG_EDIT_BUTTON_001
Mục đích kiểm thử: Kiểm tra chức năng button Hủy trong popup Chỉnh sửa
Độ ưu tiên: không có thay đổi nào được lưu

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Chọn một mẫu đánh giá trong danh sách
  3. Nhấn button Chỉnh sửa
  4. Nhấn button Hủy

Kết quả mong muốn:
  Popup Chỉnh sửa mẫu đánh giá đóng lại

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_QLMDG_EDIT_BUTTON_002
Mục đích kiểm thử: Kiểm tra trạng thái button Lưu cập nhật trong popup Chỉnh sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Chọn một mẫu đánh giá trong danh sách
  3. Nhấn button Chỉnh sửa

Kết quả mong muốn:
  Button Lưu cập nhật disable khi chưa có thay đổi thông tin trong form

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_QLMDG_EDIT_VALIDATE_001
Mục đích kiểm thử: Kiểm tra chỉnh sửa mẫu đánh giá thành công
Độ ưu tiên: Vị trí áp dụng

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Chọn một mẫu đánh giá trong danh sách
  3. Nhấn button Chỉnh sửa
  4. Thay đổi thông tin trong các trường: Tên mẫu

Kết quả mong muốn:
  Phòng áp dụng

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_QLMDG_EDIT_VALIDATE_002
Mục đích kiểm thử: Kiểm tra chỉnh sửa khi bỏ trống Tên mẫu
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Chọn một mẫu đánh giá trong danh sách
  3. Nhấn button Chỉnh sửa
  4. Xóa thông tin trong trường Tên mẫu
  5. Nhấn button Lưu cập nhật

Kết quả mong muốn:
  Hiển thị lỗi Vui lòng nhập Tên mẫu

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: TC_QLMDG_EDIT_VALIDATE_003
Mục đích kiểm thử: Kiểm tra chỉnh sửa với Tên mẫu trùng lặp
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Chọn một mẫu đánh giá trong danh sách
  3. Nhấn button Chỉnh sửa
  4. Thay đổi Tên mẫu thành một tên đã tồn tại
  5. Nhấn button Lưu cập nhật

Kết quả mong muốn:
  Hiển thị lỗi Tên mẫu đã tồn tại

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: TC_QLMDG_EDIT_VALIDATE_004
Mục đích kiểm thử: Kiểm tra ràng buộc độ dài tối đa của Tên mẫu trong popup Chỉnh sửa
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhấn button Chỉnh sửa
  3. Nhập Tên mẫu vượt quá 255 ký tự
  4. Nhấn button Lưu cập nhật

Kết quả mong muốn:
  Hiển thị lỗi Tên mẫu không được vượt quá 255 ký tự

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: TC_QLMDG_EDIT_VALIDATE_005
Mục đích kiểm thử: Kiểm tra chỉnh sửa khi chưa chọn Phòng áp dụng
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Chọn một mẫu đánh giá trong danh sách
  3. Nhấn button Chỉnh sửa
  4. Xóa thông tin trong trường Phòng áp dụng
  5. Nhấn button Lưu cập nhật

Kết quả mong muốn:
  Hiển thị lỗi Vui lòng chọn Phòng áp dụng

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: TC_QLMDG_EDIT_VALIDATE_006
Mục đích kiểm thử: Kiểm tra chỉnh sửa khi chưa chọn Vị trí áp dụng
Độ ưu tiên: High

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Chọn một mẫu đánh giá trong danh sách
  3. Nhấn button Chỉnh sửa
  4. Xóa thông tin trong trường Vị trí áp dụng
  5. Nhấn button Lưu cập nhật

Kết quả mong muốn:
  Hiển thị lỗi Vui lòng chọn Vị trí áp dụng

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: TC_QLMDG_EDIT_VALIDATE_007
Mục đích kiểm thử: Kiểm tra chức năng button Thêm câu hỏi trong popup Chỉnh sửa
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Chọn một mẫu đánh giá trong danh sách
  3. Nhấn button Chỉnh sửa
  4. Nhấn button Thêm câu hỏi

Kết quả mong muốn:
  Một câu hỏi mới được thêm vào phía dưới

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: TC_QLMDG_EDIT_VALIDATE_008
Mục đích kiểm thử: Kiểm tra chức năng button Xóa câu hỏi trong popup Chỉnh sửa
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Chọn một mẫu đánh giá trong danh sách
  3. Nhấn button Chỉnh sửa
  4. Nhấn button Thêm câu hỏi
  5. Nhấn button Xóa ở câu hỏi vừa thêm

Kết quả mong muốn:
  Câu hỏi vừa thêm bị xóa khỏi biểu mẫu

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: TC_QLMDG_EDIT_VALIDATE_009
Mục đích kiểm thử: Kiểm tra chức năng Switch bắt buộc trả lời trong popup Chỉnh sửa
Độ ưu tiên: câu hỏi có Switch đang bật sẽ là bắt buộc phải trả lời

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Chọn một mẫu đánh giá trong danh sách
  3. Nhấn button Chỉnh sửa
  4. Bật Switch ở một câu hỏi
  5. Nhấn button Lưu cập nhật

Kết quả mong muốn:
  Khi người dùng thực hiện đánh giá trên biểu mẫu này

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: TC_QLMDG_EDIT_VALIDATE_010
Mục đích kiểm thử: Kiểm tra ràng buộc độ dài tối đa của Tiêu đề câu hỏi trong popup Chỉnh sửa
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá
  2. Nhấn button Chỉnh sửa
  3. Nhập Tiêu đề câu hỏi vượt quá 255 ký tự
  4. Nhấn button Lưu cập nhật

Kết quả mong muốn:
  Hiển thị lỗi Tiêu đề câu hỏi không được vượt quá 255 ký tự

--------------------------------------------------------------------------------

Test Case #15
----------------------------------------
ID: TC_QLMDG_EDIT_VALIDATE_011
Mục đích kiểm thử: Kiểm tra các loại câu hỏi trong dropdown trong popup Chỉnh sửa
Độ ưu tiên: 

Các bước thực hiện:
  1. Click module Thử việc -> Chọn tab Setting ->

Kết quả mong muốn:

--------------------------------------------------------------------------------


Total Test Cases Generated: 42
Generated by Test Case Generator v1.0
