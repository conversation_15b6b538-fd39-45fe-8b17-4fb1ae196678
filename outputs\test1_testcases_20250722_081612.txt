================================================================================
TEST CASES - TEST1
Generated on: 2025-07-22 08:16:12
================================================================================


============================================================
**QUẢN LÝ CHỨC DANH**
============================================================



============================================================
**1. TẠO MỚI CHỨC DANH**
============================================================



============================================================
**1.1. KIỂM TRA GIAO DIỆN (UI)**
============================================================

Test Case #1
----------------------------------------
ID: TC_CD_TM_UI_001
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ Quản trị hệ thống.
  2. Nhấn nút Tạo mới chức danh.

Kết quả mong muốn:
  Popup Tạo mới chức danh hiển thị với các trường: Tên chức danh, Mô tả, nút Lưu và Hủy.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_CD_TM_UI_002
Mục đích kiểm thử: Kiểm tra hiển thị nhãn bắt buộc cho trường Tên chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới chức danh.
  2. Kiểm tra hiển thị dấu (*) hoặc nhãn bắt buộc cho trường Tên chức danh.

Kết quả mong muốn:
  Trường Tên chức danh hiển thị dấu (*) hoặc nhãn bắt buộc.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_CD_TM_UI_003
Mục đích kiểm thử: Kiểm tra kích thước tối đa của trường Tên chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới chức danh.
  2. Kiểm tra kích thước tối đa của trường Tên chức danh là 64 ký tự.

Kết quả mong muốn:
  Trường Tên chức danh giới hạn nhập tối đa 64 ký tự.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_CD_TM_UI_004
Mục đích kiểm thử: Kiểm tra kích thước tối đa của trường Mô tả
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup Tạo mới chức danh.
  2. Kiểm tra kích thước tối đa của trường Mô tả là 256 ký tự.

Kết quả mong muốn:
  Trường Mô tả giới hạn nhập tối đa 256 ký tự.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_CD_TM_UI_005
Mục đích kiểm thử: Kiểm tra hiển thị nút Lưu
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới chức danh.
  2. Kiểm tra hiển thị nút Lưu.

Kết quả mong muốn:
  Nút Lưu hiển thị.

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_CD_TM_UI_006
Mục đích kiểm thử: Kiểm tra hiển thị nút Hủy
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới chức danh.
  2. Kiểm tra hiển thị nút Hủy.

Kết quả mong muốn:
  Nút Hủy hiển thị.

--------------------------------------------------------------------------------



============================================================
**1.2. KIỂM TRA CHỨC NĂNG (FUNCTIONALITY)**
============================================================

Test Case #1
----------------------------------------
ID: TC_CD_TM_FUNC_001
Mục đích kiểm thử: Tạo mới chức danh thành công với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới chức danh.
  2. Nhập thông tin hợp lệ vào trường Tên chức danh (ví dụ: Nhân viên kinh doanh) và trường Mô tả (ví dụ: Mô tả công việc của nhân viên kinh doanh).
  3. Nhấn nút Lưu.

Kết quả mong muốn:
  Chức danh được tạo thành công và hiển thị trong danh sách chức danh. Hiển thị thông báo thành công.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_CD_TM_FUNC_002
Mục đích kiểm thử: Tạo mới chức danh thành công với trường Mô tả để trống
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới chức danh.
  2. Nhập thông tin hợp lệ vào trường Tên chức danh (ví dụ: Nhân viên kỹ thuật) và để trống trường Mô tả.
  3. Nhấn nút Lưu.

Kết quả mong muốn:
  Chức danh được tạo thành công và hiển thị trong danh sách chức danh. Hiển thị thông báo thành công.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_CD_TM_FUNC_003
Mục đích kiểm thử: Hủy tạo mới chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới chức danh.
  2. Nhập thông tin vào các trường.
  3. Nhấn nút Hủy.

Kết quả mong muốn:
  Popup Tạo mới chức danh đóng lại. Thông tin đã nhập không được lưu.

--------------------------------------------------------------------------------



============================================================
**1.3. KIỂM TRA VALIDATION (VALIDATION)**
============================================================

Test Case #1
----------------------------------------
ID: TC_CD_TM_VAL_001
Mục đích kiểm thử: Không tạo mới chức danh khi bỏ trống trường Tên chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới chức danh.
  2. Để trống trường Tên chức danh.
  3. Nhấn nút Lưu.

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Vui lòng nhập Tên chức danh. Chức danh không được tạo.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_CD_TM_VAL_002
Mục đích kiểm thử: Không tạo mới chức danh khi Tên chức danh đã tồn tại
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới chức danh.
  2. Nhập Tên chức danh đã tồn tại trong hệ thống.
  3. Nhấn nút Lưu.

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Tên chức danh đã tồn tại. Chức danh không được tạo.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_CD_TM_VAL_003
Mục đích kiểm thử: Không tạo mới chức danh khi Tên chức danh vượt quá 64 ký tự
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup Tạo mới chức danh.
  2. Nhập Tên chức danh có độ dài lớn hơn 64 ký tự.
  3. Nhấn nút Lưu.

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Tên chức danh không được vượt quá 64 ký tự. Chức danh không được tạo.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_CD_TM_VAL_004
Mục đích kiểm thử: Tạo mới chức danh khi Mô tả vượt quá 256 ký tự
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở popup Tạo mới chức danh.
  2. Nhập Mô tả có độ dài lớn hơn 256 ký tự.
  3. Nhấn nút Lưu.

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Mô tả không được vượt quá 256 ký tự. Chức danh không được tạo.

--------------------------------------------------------------------------------



============================================================
**2. SỬA CHỨC DANH**
============================================================



============================================================
**2.1. KIỂM TRA GIAO DIỆN (UI)**
============================================================

Test Case #1
----------------------------------------
ID: TC_CD_S_UI_001
Mục đích kiểm thử: Kiểm tra hiển thị popup Chỉnh sửa chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách.
  2. Nhấn nút Sửa.

Kết quả mong muốn:
  Popup Chỉnh sửa chức danh hiển thị với các trường: Tên chức danh, Mô tả (đã được điền sẵn dữ liệu cũ), nút Lưu và Hủy.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_CD_S_UI_002
Mục đích kiểm thử: Kiểm tra dữ liệu cũ được điền sẵn trong popup Chỉnh sửa chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách.
  2. Nhấn nút Sửa.

Kết quả mong muốn:
  Popup Chỉnh sửa chức danh hiển thị với trường Tên chức danh và Mô tả đã được điền sẵn dữ liệu cũ.

--------------------------------------------------------------------------------



============================================================
**2.2. KIỂM TRA CHỨC NĂNG (FUNCTIONALITY)**
============================================================

Test Case #1
----------------------------------------
ID: TC_CD_S_FUNC_001
Mục đích kiểm thử: Sửa chức danh thành công với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách.
  2. Nhấn nút Sửa.
  3. Thay đổi thông tin trong trường Tên chức danh và Mô tả.
  4. Nhấn nút Lưu.

Kết quả mong muốn:
  Chức danh được sửa thành công và hiển thị thông tin mới trong danh sách chức danh. Hiển thị thông báo thành công.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_CD_S_FUNC_002
Mục đích kiểm thử: Sửa chức danh thành công khi chỉ thay đổi trường Mô tả
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách.
  2. Nhấn nút Sửa.
  3. Chỉ thay đổi thông tin trong trường Mô tả.
  4. Nhấn nút Lưu.

Kết quả mong muốn:
  Chức danh được sửa thành công và hiển thị thông tin mới trong danh sách chức danh. Hiển thị thông báo thành công.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_CD_S_FUNC_003
Mục đích kiểm thử: Hủy sửa chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách.
  2. Nhấn nút Sửa.
  3. Thay đổi thông tin trong các trường.
  4. Nhấn nút Hủy.

Kết quả mong muốn:
  Popup Chỉnh sửa chức danh đóng lại. Thông tin đã thay đổi không được lưu.

--------------------------------------------------------------------------------



============================================================
**2.3. KIỂM TRA VALIDATION (VALIDATION)**
============================================================

Test Case #1
----------------------------------------
ID: TC_CD_S_VAL_001
Mục đích kiểm thử: Không sửa chức danh khi Tên chức danh trùng với chức danh khác
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách.
  2. Nhấn nút Sửa.
  3. Thay đổi Tên chức danh thành tên của một chức danh khác đã tồn tại (trừ chức danh đang sửa).
  4. Nhấn nút Lưu.

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Tên chức danh đã tồn tại. Chức danh không được sửa.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_CD_S_VAL_002
Mục đích kiểm thử: Không sửa chức danh khi Tên chức danh bỏ trống
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách.
  2. Nhấn nút Sửa.
  3. Xóa hết thông tin trong trường Tên chức danh.
  4. Nhấn nút Lưu.

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Vui lòng nhập Tên chức danh. Chức danh không được sửa.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_CD_S_VAL_003
Mục đích kiểm thử: Không sửa chức danh khi Tên chức danh vượt quá 64 ký tự
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách.
  2. Nhấn nút Sửa.
  3. Nhập Tên chức danh có độ dài lớn hơn 64 ký tự.
  4. Nhấn nút Lưu.

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Tên chức danh không được vượt quá 64 ký tự. Chức danh không được sửa.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_CD_S_VAL_004
Mục đích kiểm thử: Sửa chức danh khi Mô tả vượt quá 256 ký tự
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách.
  2. Nhấn nút Sửa.
  3. Nhập Mô tả có độ dài lớn hơn 256 ký tự.
  4. Nhấn nút Lưu.

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Mô tả không được vượt quá 256 ký tự. Chức danh không được sửa.

--------------------------------------------------------------------------------



============================================================
**3. XÓA CHỨC DANH**
============================================================



============================================================
**3.1. KIỂM TRA CHỨC NĂNG (FUNCTIONALITY)**
============================================================

Test Case #1
----------------------------------------
ID: TC_CD_X_FUNC_001
Mục đích kiểm thử: Hiển thị popup xác nhận xóa chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách.
  2. Nhấn nút Xóa.

Kết quả mong muốn:
  Hiển thị popup xác nhận với nội dung: Bạn có chắc chắn muốn xóa chức danh này không? và các nút Đồng ý, Hủy.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_CD_X_FUNC_002
Mục đích kiểm thử: Xóa chức danh thành công
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách (chức danh không được gán cho nhân viên).
  2. Nhấn nút Xóa.
  3. Nhấn nút Đồng ý trong popup xác nhận.

Kết quả mong muốn:
  Chức danh bị xóa khỏi danh sách. Hiển thị thông báo thành công.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_CD_X_FUNC_003
Mục đích kiểm thử: Hủy xóa chức danh
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách.
  2. Nhấn nút Xóa.
  3. Nhấn nút Hủy trong popup xác nhận.

Kết quả mong muốn:
  Popup xác nhận đóng lại. Chức danh không bị xóa.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_CD_X_FUNC_004
Mục đích kiểm thử: Không cho phép xóa chức danh đang được gán cho nhân viên
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một chức danh từ danh sách (chức danh đang được gán cho nhân viên).
  2. Nhấn nút Xóa.

Kết quả mong muốn:
  Hiển thị thông báo lỗi: Không thể xóa chức danh này vì đang được gán cho nhân viên. Chức danh không bị xóa.

--------------------------------------------------------------------------------



============================================================
**4. TÌM KIẾM CHỨC DANH**
============================================================



============================================================
**4.1. KIỂM TRA CHỨC NĂNG (FUNCTIONALITY)**
============================================================

Test Case #1
----------------------------------------
ID: TC_CD_TK_FUNC_001
Mục đích kiểm thử: Tìm kiếm chức danh theo tên (chính xác)
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập tên chức danh (chính xác) vào ô tìm kiếm.
  2. Kiểm tra kết quả tìm kiếm.

Kết quả mong muốn:
  Danh sách chức danh hiển thị các chức danh có tên trùng khớp với từ khóa tìm kiếm.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_CD_TK_FUNC_002
Mục đích kiểm thử: Tìm kiếm chức danh theo tên (không phân biệt hoa thường)
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập tên chức danh (viết hoa hoặc viết thường) vào ô tìm kiếm.
  2. Kiểm tra kết quả tìm kiếm.

Kết quả mong muốn:
  Danh sách chức danh hiển thị các chức danh có tên trùng khớp với từ khóa tìm kiếm (không phân biệt hoa thường).

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_CD_TK_FUNC_003
Mục đích kiểm thử: Tìm kiếm chức danh theo tên (một phần)
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập một phần tên chức danh vào ô tìm kiếm.
  2. Kiểm tra kết quả tìm kiếm.

Kết quả mong muốn:
  Danh sách chức danh hiển thị các chức danh có tên chứa từ khóa tìm kiếm.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_CD_TK_FUNC_004
Mục đích kiểm thử: Tìm kiếm chức danh không tìm thấy kết quả
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Nhập một từ khóa không trùng khớp với bất kỳ tên chức danh nào vào ô tìm kiếm.
  2. Kiểm tra kết quả tìm kiếm.

Kết quả mong muốn:
  Danh sách chức danh hiển thị thông báo Không tìm thấy kết quả.

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_CD_TK_FUNC_005
Mục đích kiểm thử: Xóa từ khóa tìm kiếm
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập từ khóa vào ô tìm kiếm.
  2. Xóa từ khóa tìm kiếm.

Kết quả mong muốn:
  Danh sách chức danh hiển thị đầy đủ các chức danh.

--------------------------------------------------------------------------------



============================================================
**QUẢN LÝ MẪU ĐÁNH GIÁ**
============================================================



============================================================
**ĐIỀU KIỆN TIÊN QUYẾT**
============================================================

Test Case #1
----------------------------------------
ID: TC_MDDG_TDK_001
Mục đích kiểm thử: Kiểm tra quyền truy cập trang Quản lý mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống với tài khoản không có quyền Quản lý thử việc.
  2. Truy cập module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá.

Kết quả mong muốn:
  Hệ thống không cho phép truy cập trang Quản lý mẫu đánh giá hoặc không hiển thị tab Mẫu đánh giá.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_MDDG_TDK_002
Mục đích kiểm thử: Kiểm tra quyền truy cập trang Quản lý mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập vào hệ thống với tài khoản có quyền Quản lý thử việc.
  2. Truy cập module Thử việc -> Chọn tab Setting -> chọn tab Mẫu đánh giá.

Kết quả mong muốn:
  Hệ thống cho phép truy cập trang Quản lý mẫu đánh giá.

--------------------------------------------------------------------------------



============================================================
**1. MÀN HÌNH QUẢN LÝ MẪU ĐÁNH GIÁ**
============================================================



============================================================
**1.1. KIỂM TRA TRẠNG THÁI BAN ĐẦU**
============================================================

Test Case #1
----------------------------------------
ID: TC_MDDG_QLMDDG_001
Mục đích kiểm thử: Kiểm tra trạng thái enable/disable của các button và input khi load trang
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập trang Quản lý Mẫu đánh giá.

Kết quả mong muốn:
  Các input/button đều enable, trừ button Lưu/Lưu cập nhật disable.

--------------------------------------------------------------------------------



============================================================
**1.2. KIỂM TRA CHỨC NĂNG CỦA BỘ LỌC**
============================================================

Test Case #1
----------------------------------------
ID: TC_MDDG_QLMDDG_002
Mục đích kiểm thử: Kiểm tra hiển thị ô input nhập điều kiện tìm kiếm
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập trang Quản lý Mẫu đánh giá.

Kết quả mong muốn:
  Hiển thị ô input nhập điều kiện tìm kiếm.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_MDDG_QLMDDG_003
Mục đích kiểm thử: Tìm kiếm theo Phòng ban
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập thông tin Phòng ban vào ô tìm kiếm.
  2. Kiểm tra kết quả tìm kiếm.

Kết quả mong muốn:
  Danh sách mẫu đánh giá hiển thị các mẫu đánh giá thuộc Phòng ban đã chọn.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_MDDG_QLMDDG_004
Mục đích kiểm thử: Tìm kiếm theo Vị trí
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập thông tin Vị trí vào ô tìm kiếm.
  2. Kiểm tra kết quả tìm kiếm.

Kết quả mong muốn:
  Danh sách mẫu đánh giá hiển thị các mẫu đánh giá thuộc Vị trí đã chọn.

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_MDDG_QLMDDG_005
Mục đích kiểm thử: Tìm kiếm không tìm thấy kết quả
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Nhập thông tin không tồn tại vào ô tìm kiếm.
  2. Kiểm tra kết quả tìm kiếm.

Kết quả mong muốn:
  Danh sách mẫu đánh giá hiển thị thông báo Không tìm thấy kết quả.

--------------------------------------------------------------------------------



============================================================
**1.3. KIỂM TRA CHỨC NĂNG CỦA BUTTON THÊM MỚI**
============================================================

Test Case #1
----------------------------------------
ID: TC_MDDG_QLMDDG_006
Mục đích kiểm thử: Kiểm tra hiển thị popup tạo mới biểu mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập trang Quản lý Mẫu đánh giá.
  2. Nhấn button Thêm mới.

Kết quả mong muốn:
  Hiển thị popup tạo mới biểu mẫu đánh giá.

--------------------------------------------------------------------------------



============================================================
**1.4. KIỂM TRA HIỂN THỊ THÔNG TIN MẪU ĐÁNH GIÁ**
============================================================

Test Case #1
----------------------------------------
ID: TC_MDDG_QLMDDG_007
Mục đích kiểm thử: Kiểm tra hiển thị Tên mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập trang Quản lý Mẫu đánh giá.

Kết quả mong muốn:
  Hiển thị Tên mẫu đánh giá đã được đăng ký.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_MDDG_QLMDDG_008
Mục đích kiểm thử: Kiểm tra hiển thị Phòng ban áp dụng
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập trang Quản lý Mẫu đánh giá.

Kết quả mong muốn:
  Hiển thị Phòng ban áp dụng đã được đăng ký.

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_MDDG_QLMDDG_009
Mục đích kiểm thử: Kiểm tra hiển thị Vị trí áp dụng
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập trang Quản lý Mẫu đánh giá.

Kết quả mong muốn:
  Hiển thị Vị trí áp dụng đã được đăng ký.

--------------------------------------------------------------------------------



============================================================
**1.5. KIỂM TRA CHỨC NĂNG CỦA BUTTON CHỈNH SỬA**
============================================================

Test Case #1
----------------------------------------
ID: TC_MDDG_QLMDDG_010
Mục đích kiểm thử: Kiểm tra hiển thị popup chỉnh sửa biểu mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một mẫu đánh giá.
  2. Nhấn button Chỉnh sửa.

Kết quả mong muốn:
  Hiển thị popup Chỉnh sửa biểu mẫu đánh giá.

--------------------------------------------------------------------------------



============================================================
**1.6. KIỂM TRA CHỨC NĂNG CỦA BUTTON XÓA**
============================================================

Test Case #1
----------------------------------------
ID: TC_MDDG_QLMDDG_011
Mục đích kiểm thử: Kiểm tra hiển thị popup xác nhận xóa biểu mẫu đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn một mẫu đánh giá.
  2. Nhấn button Xóa.

Kết quả mong muốn:
  Hiển thị popup xác nhận Bạn có chắc chắn muốn xóa đối tượng này không?.

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_MDDG_QLMDDG_012
Mục đích kiểm thử: Xóa biểu mẫu đánh giá thành công
Độ ưu tiên: 

Các bước thực hiện:
  1. Chọn một mẫu đánh giá.
  2. Nhấn button Xóa.
  3. Nhấn button Xóa trong

Kết quả mong muốn:

--------------------------------------------------------------------------------


Total Test Cases Generated: 45
Generated by Test Case Generator v1.0
